import asyncio
import csv
import os
from typing import List, Dict, Any
import httpx


async def fetch_page(client: httpx.AsyncClient, base_url: str, page_number: int, page_size: int = 100) -> List[Dict[str, Any]]:
    """Fetch a single page of resource_cd's from the API."""
    params = {
        "limit": page_size,
        "offset": page_number * page_size,
    }

    response = await client.get(f"{base_url}/resources", params=params)
    response.raise_for_status()
    return response.json()


async def fetch_all_pages(base_url: str, page_size: int = 100) -> List[tuple[str, int]]:
    """Fetch all pages of resource_cd's concurrently."""

    async with httpx.AsyncClient(timeout=30.0) as client:
        # First, determine how many pages we need by fetching the first page
        first_page = await fetch_page(client, base_url, 0, page_size)

        if not first_page:
            return []

        # If first page has fewer items than page_size, we only have one page
        if len(first_page) < page_size:
            return [(item["resource_cd"], 0) for item in first_page]

        # Estimate total pages by fetching pages until we get a partial page
        # We'll do this by fetching pages sequentially until we find the last one
        page_number = 0
        all_results = []

        while True:
            page_data = await fetch_page(client, base_url, page_number, page_size)

            # Add results from this page with their page number
            for item in page_data:
                all_results.append((item["resource_cd"], page_number))

            # If this page has fewer items than page_size, it's the last page
            if len(page_data) < page_size:
                break

            page_number += 1

        return all_results


async def fetch_all_pages_concurrent(base_url: str, page_size: int = 100) -> List[tuple[str, int]]:
    """Fetch all pages concurrently after determining the total number of pages."""

    async with httpx.AsyncClient(timeout=30.0) as client:
        # First, determine the total number of pages by fetching sequentially
        # until we find a page with fewer than page_size items
        page_number = 0
        total_pages = 0

        while True:
            page_data = await fetch_page(client, base_url, page_number, page_size)

            if len(page_data) < page_size:
                total_pages = page_number + 1
                break
            elif len(page_data) == 0:
                total_pages = page_number
                break

            page_number += 1

        if total_pages == 0:
            return []

        # Now fetch all pages concurrently
        tasks = [
            fetch_page(client, base_url, page_num, page_size)
            for page_num in range(total_pages)
        ]

        pages_data = await asyncio.gather(*tasks)

        # Flatten results while preserving order and adding page numbers
        all_results = []
        for page_num, page_data in enumerate(pages_data):
            for item in page_data:
                all_results.append((item["resource_cd"], page_num))

        return all_results


def save_to_csv(results: List[tuple[str, int]], filename: str = "resource_cds.csv"):
    """Save results to CSV file."""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['resource_cd', 'page_number'])
        writer.writerows(results)

    print(f"Saved {len(results)} resource_cd's to {filename}")


async def main():
    """Main function to fetch all resource_cd's and save to CSV."""

    # Try to get base URL from environment variables (following test pattern)
    base_url = (
        os.getenv("LOCAL_CEH_PROVIDER_URL") or
        os.getenv("DOCKER_CEH_PROVIDER_URL") or
        os.getenv("LOCAL_UNI_PROVIDER_URL") or
        os.getenv("DOCKER_UNI_PROVIDER_URL") or
        "http://localhost:8000"  # Default fallback
    )

    print(f"Fetching resource_cd's from: {base_url}")

    try:
        # Use the concurrent version for better performance
        results = await fetch_all_pages_concurrent(base_url)

        if not results:
            print("No resource_cd's found")
            return

        print(f"Fetched {len(results)} resource_cd's from {max(page for _, page in results) + 1} pages")

        # Save to CSV
        save_to_csv(results)

        # Print some statistics
        pages_count = {}
        for _, page_num in results:
            pages_count[page_num] = pages_count.get(page_num, 0) + 1

        print("\nPage statistics:")
        for page_num in sorted(pages_count.keys()):
            print(f"  Page {page_num}: {pages_count[page_num]} resource_cd's")

    except httpx.HTTPStatusError as e:
        print(f"HTTP error: {e.response.status_code} - {e.response.text}")
    except httpx.RequestError as e:
        print(f"Request error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    asyncio.run(main())